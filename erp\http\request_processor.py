"""
Request processing utilities for HTTP routes
"""

import inspect
from typing import Any, <PERSON><PERSON>, <PERSON><PERSON>
from fastapi import Request, HTTPException

from ..logging import get_logger

logger = get_logger(__name__)


class RequestProcessor:
    """Handles request processing for HTTP routes"""
    
    @staticmethod
    def extract_request_and_controller(args: tuple, kwargs: dict) -> <PERSON><PERSON>[Optional[Request], Optional[Any]]:
        """
        Extract request object and controller instance from function arguments
        
        Args:
            args: Function positional arguments
            kwargs: Function keyword arguments
            
        Returns:
            Tuple of (request, controller_instance)
            
        Raises:
            HTTPException: If request object cannot be found
        """
        request = None
        controller_instance = None
        
        # Check if first arg is Request
        if args and isinstance(args[0], Request):
            request = args[0]
        # Check if request is in kwargs
        elif 'request' in kwargs:
            request = kwargs['request']
        # Check if first arg is controller instance with request
        elif args and hasattr(args[0], '_current_request'):
            controller_instance = args[0]
            if len(args) > 1 and isinstance(args[1], Request):
                request = args[1]
            elif 'request' in kwargs:
                request = kwargs['request']
        
        if not request:
            raise HTTPException(status_code=500, detail="Request object not found")
        
        return request, controller_instance
    
    @staticmethod
    async def process_form_data(request: Request) -> None:
        """
        Process form data for POST requests and attach to request object
        
        Args:
            request: FastAPI request object
        """
        if (request.method == "POST" and 
            request.headers.get("content-type", "").startswith("application/x-www-form-urlencoded")):
            try:
                form_data = await request.form()
                request._form_data = dict(form_data)
            except Exception as e:
                logger.debug(f"Failed to process form data: {e}")
                request._form_data = {}
        else:
            request._form_data = {}
    
    @staticmethod
    def inject_context_into_controller(controller_instance: Any, request: Request, environment: Any) -> None:
        """
        Inject request and environment context into controller instance
        
        Args:
            controller_instance: Controller instance to inject context into
            request: FastAPI request object
            environment: Current environment context
        """
        if controller_instance and hasattr(controller_instance, '_set_request_context'):
            controller_instance._set_request_context(request, environment)
    
    @staticmethod
    def clear_controller_context(controller_instance: Any) -> None:
        """
        Clear request context from controller instance
        
        Args:
            controller_instance: Controller instance to clear context from
        """
        if controller_instance and hasattr(controller_instance, '_clear_request_context'):
            controller_instance._clear_request_context()
    
    @staticmethod
    def get_current_environment():
        """
        Get current environment from context manager
        
        Returns:
            Current environment or None
        """
        try:
            from ..context import ContextManager
            return ContextManager.get_environment()
        except Exception as e:
            logger.debug(f"Failed to get current environment: {e}")
            return None


class RequestContextManager:
    """Context manager for handling request processing lifecycle"""
    
    def __init__(self, args: tuple, kwargs: dict):
        self.args = args
        self.kwargs = kwargs
        self.request = None
        self.controller_instance = None
        self.environment = None
    
    async def __aenter__(self):
        """Enter context and setup request processing"""
        # Extract request and controller
        self.request, self.controller_instance = RequestProcessor.extract_request_and_controller(
            self.args, self.kwargs
        )
        
        # Process form data
        await RequestProcessor.process_form_data(self.request)
        
        # Get current environment
        self.environment = RequestProcessor.get_current_environment()
        
        # Inject context into controller
        RequestProcessor.inject_context_into_controller(
            self.controller_instance, self.request, self.environment
        )
        
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Exit context and cleanup"""
        # Clear controller context
        RequestProcessor.clear_controller_context(self.controller_instance)
    
    def get_request(self) -> Request:
        """Get the processed request object"""
        return self.request
    
    def get_controller(self) -> Optional[Any]:
        """Get the controller instance"""
        return self.controller_instance
    
    def get_environment(self) -> Optional[Any]:
        """Get the current environment"""
        return self.environment


def create_request_context(args: tuple, kwargs: dict) -> RequestContextManager:
    """
    Create a request context manager for processing
    
    Args:
        args: Function positional arguments
        kwargs: Function keyword arguments
        
    Returns:
        RequestContextManager instance
    """
    return RequestContextManager(args, kwargs)
