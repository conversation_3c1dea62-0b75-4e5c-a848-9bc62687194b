"""
Route discovery service
Discovers and organizes routes from different sources
"""

import importlib
import inspect
import pkgutil
from typing import Dict, List, Optional, Set, Any, Callable
from pathlib import Path

from .interfaces import IRouteDiscovery, RouteInfo, RouteScope
from .factories import get_route_info_factory
from .metadata import RouteType, AuthType
from ..logging import get_logger

logger = get_logger(__name__)


class RouteDiscoveryService(IRouteDiscovery):
    """Service for discovering routes from various sources"""
    
    def __init__(self):
        self.route_info_factory = get_route_info_factory()
        self.logger = get_logger(__name__)
        self._discovered_sources: Set[str] = set()
    
    async def discover_routes(self, source: str, scope: RouteScope) -> List[RouteInfo]:
        """
        Discover routes from a source
        
        Args:
            source: Source identifier (module name, package name, etc.)
            scope: Route scope (system, database, addon)
            
        Returns:
            List of discovered routes
        """
        try:
            self.logger.debug(f"Discovering routes from {source} with scope {scope}")
            
            routes = []
            
            # Determine discovery method based on source type
            if self._is_package(source):
                routes = await self._discover_routes_in_package(source, scope)
            elif self._is_module(source):
                routes = await self._discover_routes_in_module(source, scope)
            elif self._is_directory(source):
                routes = await self._discover_routes_in_directory(source, scope)
            else:
                self.logger.warning(f"Unknown source type: {source}")
            
            # Track discovered source
            self._discovered_sources.add(source)
            
            self.logger.info(f"Discovered {len(routes)} routes from {source}")
            return routes
            
        except Exception as e:
            self.logger.error(f"Error discovering routes from {source}: {e}")
            return []
    
    async def refresh_routes(self, source: str) -> List[RouteInfo]:
        """
        Refresh routes from a source
        
        Args:
            source: Source identifier
            
        Returns:
            List of refreshed routes
        """
        try:
            # Determine scope based on source characteristics
            scope = self._determine_scope(source)
            
            # Re-discover routes
            return await self.discover_routes(source, scope)
            
        except Exception as e:
            self.logger.error(f"Error refreshing routes from {source}: {e}")
            return []
    
    def _is_package(self, source: str) -> bool:
        """Check if source is a Python package"""
        try:
            module = importlib.import_module(source)
            return hasattr(module, '__path__')
        except ImportError:
            return False
    
    def _is_module(self, source: str) -> bool:
        """Check if source is a Python module"""
        try:
            importlib.import_module(source)
            return True
        except ImportError:
            return False
    
    def _is_directory(self, source: str) -> bool:
        """Check if source is a directory path"""
        return Path(source).is_dir()
    
    def _determine_scope(self, source: str) -> RouteScope:
        """Determine route scope based on source characteristics"""
        # Simple heuristics - can be made more sophisticated
        if 'addon' in source.lower() or 'module' in source.lower():
            return RouteScope.ADDON
        elif 'database' in source.lower() or 'db' in source.lower():
            return RouteScope.DATABASE
        else:
            return RouteScope.SYSTEM
    
    async def _discover_routes_in_package(self, package_name: str, scope: RouteScope) -> List[RouteInfo]:
        """Discover routes in a Python package"""
        routes = []
        
        try:
            package = importlib.import_module(package_name)
            
            # Iterate through all modules in the package
            for importer, modname, ispkg in pkgutil.iter_modules(package.__path__, package_name + "."):
                if not ispkg:  # Only scan modules, not sub-packages
                    module_routes = await self._discover_routes_in_module(modname, scope)
                    routes.extend(module_routes)
            
        except Exception as e:
            self.logger.error(f"Error discovering routes in package {package_name}: {e}")
        
        return routes
    
    async def _discover_routes_in_module(self, module_name: str, scope: RouteScope) -> List[RouteInfo]:
        """Discover routes in a Python module"""
        routes = []
        
        try:
            module = importlib.import_module(module_name)
            
            # Look for functions with route decorators
            for name, obj in inspect.getmembers(module, inspect.isfunction):
                route_info = self._extract_route_info_from_function(obj, scope, module_name)
                if route_info:
                    routes.append(route_info)
            
            # Look for controller classes
            for name, obj in inspect.getmembers(module, inspect.isclass):
                class_routes = self._extract_routes_from_controller(obj, scope, module_name)
                routes.extend(class_routes)
            
        except Exception as e:
            self.logger.error(f"Error discovering routes in module {module_name}: {e}")
        
        return routes
    
    async def _discover_routes_in_directory(self, directory: str, scope: RouteScope) -> List[RouteInfo]:
        """Discover routes in a directory"""
        routes = []
        
        try:
            dir_path = Path(directory)
            
            # Find all Python files
            for py_file in dir_path.rglob("*.py"):
                if py_file.name.startswith('__'):
                    continue
                
                # Convert file path to module name
                relative_path = py_file.relative_to(dir_path)
                module_parts = list(relative_path.parts[:-1]) + [relative_path.stem]
                module_name = '.'.join(module_parts)
                
                # Try to discover routes in the module
                try:
                    module_routes = await self._discover_routes_in_module(module_name, scope)
                    routes.extend(module_routes)
                except Exception as e:
                    self.logger.debug(f"Could not discover routes in {module_name}: {e}")
        
        except Exception as e:
            self.logger.error(f"Error discovering routes in directory {directory}: {e}")
        
        return routes
    
    def _extract_route_info_from_function(self, func: Callable, scope: RouteScope, source: str) -> Optional[RouteInfo]:
        """Extract route info from a decorated function"""
        try:
            # Check if function has route metadata
            if hasattr(func, '_route_metadata'):
                metadata = func._route_metadata
                
                return self.route_info_factory.create_route_info(
                    path=metadata.get('path', f"/{func.__name__}"),
                    handler=func,
                    route_type=metadata.get('type', RouteType.HTTP),
                    auth=metadata.get('auth', AuthType.USER),
                    methods=metadata.get('methods'),
                    scope=scope,
                    source=source,
                    **{k: v for k, v in metadata.items() if k not in ['path', 'type', 'auth', 'methods']}
                )
            
            return None
            
        except Exception as e:
            self.logger.debug(f"Error extracting route info from function {func.__name__}: {e}")
            return None
    
    def _extract_routes_from_controller(self, controller_class: type, scope: RouteScope, source: str) -> List[RouteInfo]:
        """Extract routes from a controller class"""
        routes = []
        
        try:
            # Check if it's a controller class
            from .controllers.base import BaseController
            if not issubclass(controller_class, BaseController):
                return routes
            
            # Look for methods with route decorators
            for name, method in inspect.getmembers(controller_class, inspect.isfunction):
                if hasattr(method, '_route_metadata'):
                    route_info = self._extract_route_info_from_function(method, scope, source)
                    if route_info:
                        routes.append(route_info)
            
        except Exception as e:
            self.logger.debug(f"Error extracting routes from controller {controller_class.__name__}: {e}")
        
        return routes
    
    def get_discovered_sources(self) -> Set[str]:
        """Get set of discovered sources"""
        return self._discovered_sources.copy()
    
    def clear_discovered_sources(self) -> None:
        """Clear discovered sources tracking"""
        self._discovered_sources.clear()


class RouteOrganizer:
    """Organizes discovered routes by various criteria"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
    
    def organize_by_scope(self, routes: List[RouteInfo]) -> Dict[RouteScope, List[RouteInfo]]:
        """Organize routes by scope"""
        organized = {scope: [] for scope in RouteScope}
        
        for route in routes:
            organized[route.scope].append(route)
        
        return organized
    
    def organize_by_source(self, routes: List[RouteInfo]) -> Dict[str, List[RouteInfo]]:
        """Organize routes by source"""
        organized = {}
        
        for route in routes:
            source = route.source or 'unknown'
            if source not in organized:
                organized[source] = []
            organized[source].append(route)
        
        return organized
    
    def organize_by_type(self, routes: List[RouteInfo]) -> Dict[RouteType, List[RouteInfo]]:
        """Organize routes by type"""
        organized = {route_type: [] for route_type in RouteType}
        
        for route in routes:
            organized[route.route_type].append(route)
        
        return organized
    
    def organize_by_path_prefix(self, routes: List[RouteInfo]) -> Dict[str, List[RouteInfo]]:
        """Organize routes by path prefix"""
        organized = {}
        
        for route in routes:
            # Extract first path segment as prefix
            path_parts = route.path.strip('/').split('/')
            prefix = '/' + (path_parts[0] if path_parts and path_parts[0] else '')
            
            if prefix not in organized:
                organized[prefix] = []
            organized[prefix].append(route)
        
        return organized
    
    def filter_routes(self, routes: List[RouteInfo], **criteria) -> List[RouteInfo]:
        """Filter routes based on criteria"""
        filtered = routes
        
        for key, value in criteria.items():
            if key == 'scope':
                filtered = [r for r in filtered if r.scope == value]
            elif key == 'source':
                filtered = [r for r in filtered if r.source == value]
            elif key == 'route_type':
                filtered = [r for r in filtered if r.route_type == value]
            elif key == 'auth':
                filtered = [r for r in filtered if r.auth == value]
            elif key == 'methods':
                if isinstance(value, list):
                    filtered = [r for r in filtered if any(m in r.methods for m in value)]
                else:
                    filtered = [r for r in filtered if value in r.methods]
            elif key == 'path_prefix':
                filtered = [r for r in filtered if r.path.startswith(value)]
        
        return filtered


# Global discovery service
_discovery_service = RouteDiscoveryService()
_route_organizer = RouteOrganizer()


def get_route_discovery_service() -> RouteDiscoveryService:
    """Get the global route discovery service"""
    return _discovery_service


def get_route_organizer() -> RouteOrganizer:
    """Get the global route organizer"""
    return _route_organizer
