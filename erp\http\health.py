"""
Health check service
Provides health monitoring capabilities for routes and registries
"""

import asyncio
import time
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from enum import Enum

from .interfaces import IHealthCheck, IRouteRegistry, RouteInfo
from ..logging import get_logger

logger = get_logger(__name__)


class HealthStatus(Enum):
    """Health status enumeration"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheckResult:
    """Result of a health check"""
    status: HealthStatus
    message: str
    details: Dict[str, Any]
    timestamp: float
    duration_ms: float


class RouteHealthChecker(IHealthCheck):
    """Service for checking route and registry health"""
    
    def __init__(self):
        self.logger = get_logger(__name__)
        self._check_timeout = 30.0  # 30 seconds
        self._cache_ttl = 60.0  # 1 minute
        self._health_cache: Dict[str, HealthCheckResult] = {}
    
    async def check_route_health(self, route_info: RouteInfo) -> Dict[str, Any]:
        """
        Check health of a specific route
        
        Args:
            route_info: Route to check
            
        Returns:
            Health check result
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"route:{route_info.path}:{':'.join(route_info.methods)}"
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return asdict(cached_result)
            
            # Perform health checks
            checks = await self._perform_route_checks(route_info)
            
            # Determine overall status
            status = self._determine_overall_status(checks)
            
            # Create result
            duration_ms = (time.time() - start_time) * 1000
            result = HealthCheckResult(
                status=status,
                message=self._generate_status_message(status, checks),
                details=checks,
                timestamp=time.time(),
                duration_ms=duration_ms
            )
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return asdict(result)
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            error_result = HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Health check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=time.time(),
                duration_ms=duration_ms
            )
            return asdict(error_result)
    
    async def check_registry_health(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """
        Check health of entire registry
        
        Args:
            registry: Registry to check
            
        Returns:
            Health check result
        """
        start_time = time.time()
        
        try:
            # Check cache first
            cache_key = f"registry:{id(registry)}"
            cached_result = self._get_cached_result(cache_key)
            if cached_result:
                return asdict(cached_result)
            
            # Perform registry checks
            checks = await self._perform_registry_checks(registry)
            
            # Determine overall status
            status = self._determine_overall_status(checks)
            
            # Create result
            duration_ms = (time.time() - start_time) * 1000
            result = HealthCheckResult(
                status=status,
                message=self._generate_status_message(status, checks),
                details=checks,
                timestamp=time.time(),
                duration_ms=duration_ms
            )
            
            # Cache result
            self._cache_result(cache_key, result)
            
            return asdict(result)
            
        except Exception as e:
            duration_ms = (time.time() - start_time) * 1000
            error_result = HealthCheckResult(
                status=HealthStatus.UNHEALTHY,
                message=f"Registry health check failed: {str(e)}",
                details={'error': str(e)},
                timestamp=time.time(),
                duration_ms=duration_ms
            )
            return asdict(error_result)
    
    async def _perform_route_checks(self, route_info: RouteInfo) -> Dict[str, Any]:
        """Perform various health checks on a route"""
        checks = {}
        
        # Handler availability check
        checks['handler_available'] = await self._check_handler_availability(route_info.handler)
        
        # Handler responsiveness check
        checks['handler_responsive'] = await self._check_handler_responsiveness(route_info.handler)
        
        # Dependencies check
        checks['dependencies'] = await self._check_route_dependencies(route_info)
        
        # Configuration check
        checks['configuration'] = await self._check_route_configuration(route_info)
        
        return checks
    
    async def _perform_registry_checks(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Perform various health checks on a registry"""
        checks = {}
        
        # Registry accessibility check
        checks['accessible'] = await self._check_registry_accessibility(registry)
        
        # Route count check
        checks['route_count'] = await self._check_route_count(registry)
        
        # Route validation check
        checks['route_validation'] = await self._check_route_validation(registry)
        
        # Performance check
        checks['performance'] = await self._check_registry_performance(registry)
        
        return checks
    
    async def _check_handler_availability(self, handler: Any) -> Dict[str, Any]:
        """Check if handler is available and callable"""
        try:
            if not callable(handler):
                return {
                    'status': HealthStatus.UNHEALTHY.value,
                    'message': 'Handler is not callable'
                }
            
            # Check if handler module is importable
            if hasattr(handler, '__module__'):
                try:
                    import importlib
                    importlib.import_module(handler.__module__)
                except ImportError as e:
                    return {
                        'status': HealthStatus.UNHEALTHY.value,
                        'message': f'Handler module not importable: {str(e)}'
                    }
            
            return {
                'status': HealthStatus.HEALTHY.value,
                'message': 'Handler is available'
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'message': f'Handler availability check failed: {str(e)}'
            }
    
    async def _check_handler_responsiveness(self, handler: Any) -> Dict[str, Any]:
        """Check if handler responds within reasonable time"""
        try:
            # This is a simplified check - in a real system you might
            # want to make a test request to the handler
            
            # For now, just check if it's an async function
            import inspect
            if inspect.iscoroutinefunction(handler):
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': 'Handler is async (good for responsiveness)'
                }
            else:
                return {
                    'status': HealthStatus.DEGRADED.value,
                    'message': 'Handler is synchronous (may block)'
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Responsiveness check failed: {str(e)}'
            }
    
    async def _check_route_dependencies(self, route_info: RouteInfo) -> Dict[str, Any]:
        """Check route dependencies"""
        try:
            dependencies = route_info.metadata.get('dependencies', [])
            
            if not dependencies:
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': 'No dependencies to check'
                }
            
            # Check each dependency
            failed_deps = []
            for dep in dependencies:
                # This is a simplified check - you'd implement actual dependency checking
                if not await self._check_dependency(dep):
                    failed_deps.append(str(dep))
            
            if failed_deps:
                return {
                    'status': HealthStatus.UNHEALTHY.value,
                    'message': f'Failed dependencies: {failed_deps}'
                }
            else:
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': 'All dependencies available'
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Dependency check failed: {str(e)}'
            }
    
    async def _check_route_configuration(self, route_info: RouteInfo) -> Dict[str, Any]:
        """Check route configuration"""
        try:
            # Validate basic configuration
            if not route_info.path or not route_info.methods:
                return {
                    'status': HealthStatus.UNHEALTHY.value,
                    'message': 'Invalid route configuration'
                }
            
            return {
                'status': HealthStatus.HEALTHY.value,
                'message': 'Route configuration is valid'
            }
            
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Configuration check failed: {str(e)}'
            }
    
    async def _check_registry_accessibility(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Check if registry is accessible"""
        try:
            # Try to get routes from registry
            await asyncio.wait_for(registry.get_routes(), timeout=self._check_timeout)
            
            return {
                'status': HealthStatus.HEALTHY.value,
                'message': 'Registry is accessible'
            }
            
        except asyncio.TimeoutError:
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'message': 'Registry access timed out'
            }
        except Exception as e:
            return {
                'status': HealthStatus.UNHEALTHY.value,
                'message': f'Registry not accessible: {str(e)}'
            }
    
    async def _check_route_count(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Check route count in registry"""
        try:
            routes = await registry.get_routes()
            total_routes = sum(len(route_list) for route_list in routes.values())
            
            if total_routes == 0:
                return {
                    'status': HealthStatus.DEGRADED.value,
                    'message': 'No routes registered',
                    'count': total_routes
                }
            else:
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': f'{total_routes} routes registered',
                    'count': total_routes
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Route count check failed: {str(e)}'
            }
    
    async def _check_route_validation(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Check route validation in registry"""
        try:
            from .validation import get_route_validator
            validator = get_route_validator()
            
            validation_results = await validator.validate_registry(registry)
            
            if validation_results:
                error_count = len(validation_results)
                return {
                    'status': HealthStatus.DEGRADED.value,
                    'message': f'{error_count} validation errors found',
                    'errors': validation_results
                }
            else:
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': 'All routes pass validation'
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Validation check failed: {str(e)}'
            }
    
    async def _check_registry_performance(self, registry: IRouteRegistry) -> Dict[str, Any]:
        """Check registry performance"""
        try:
            start_time = time.time()
            await registry.get_routes()
            duration = time.time() - start_time
            
            if duration > 1.0:  # More than 1 second
                return {
                    'status': HealthStatus.DEGRADED.value,
                    'message': f'Registry access slow ({duration:.2f}s)',
                    'duration': duration
                }
            else:
                return {
                    'status': HealthStatus.HEALTHY.value,
                    'message': f'Registry access fast ({duration:.2f}s)',
                    'duration': duration
                }
                
        except Exception as e:
            return {
                'status': HealthStatus.UNKNOWN.value,
                'message': f'Performance check failed: {str(e)}'
            }
    
    async def _check_dependency(self, dependency: Any) -> bool:
        """Check if a dependency is available"""
        # Simplified dependency check - implement based on your needs
        return True
    
    def _determine_overall_status(self, checks: Dict[str, Any]) -> HealthStatus:
        """Determine overall status from individual checks"""
        statuses = []
        
        for check in checks.values():
            if isinstance(check, dict) and 'status' in check:
                try:
                    status = HealthStatus(check['status'])
                    statuses.append(status)
                except ValueError:
                    statuses.append(HealthStatus.UNKNOWN)
        
        if not statuses:
            return HealthStatus.UNKNOWN
        
        # If any check is unhealthy, overall is unhealthy
        if HealthStatus.UNHEALTHY in statuses:
            return HealthStatus.UNHEALTHY
        
        # If any check is degraded, overall is degraded
        if HealthStatus.DEGRADED in statuses:
            return HealthStatus.DEGRADED
        
        # If all checks are healthy, overall is healthy
        if all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        
        # Otherwise unknown
        return HealthStatus.UNKNOWN
    
    def _generate_status_message(self, status: HealthStatus, checks: Dict[str, Any]) -> str:
        """Generate a status message based on checks"""
        if status == HealthStatus.HEALTHY:
            return "All checks passed"
        elif status == HealthStatus.DEGRADED:
            return "Some checks show degraded performance"
        elif status == HealthStatus.UNHEALTHY:
            return "Critical issues detected"
        else:
            return "Health status unknown"
    
    def _get_cached_result(self, cache_key: str) -> Optional[HealthCheckResult]:
        """Get cached health check result"""
        if cache_key in self._health_cache:
            result = self._health_cache[cache_key]
            if time.time() - result.timestamp < self._cache_ttl:
                return result
            else:
                del self._health_cache[cache_key]
        return None
    
    def _cache_result(self, cache_key: str, result: HealthCheckResult) -> None:
        """Cache health check result"""
        self._health_cache[cache_key] = result
        
        # Clean up old cache entries
        current_time = time.time()
        expired_keys = [
            key for key, cached_result in self._health_cache.items()
            if current_time - cached_result.timestamp > self._cache_ttl
        ]
        for key in expired_keys:
            del self._health_cache[key]


# Global health checker instance
_health_checker = RouteHealthChecker()


def get_health_checker() -> RouteHealthChecker:
    """Get the global health checker"""
    return _health_checker
