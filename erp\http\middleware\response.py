"""
Response processing middleware
Handles response formatting, serialization, and headers
"""

from typing import Any, Dict, Optional

from .base import BaseMiddleware
from ..interfaces import RouteInfo
from ..metadata import RouteType
from ...logging import get_logger

logger = get_logger(__name__)


class ResponseProcessingMiddleware(BaseMiddleware):
    """Middleware for processing outgoing responses"""
    
    def __init__(self, priority: int = 85):
        super().__init__(priority)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Pass through request"""
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Process outgoing response"""
        try:
            # Format response based on route type
            formatted_response = await self._format_response(response, route_info)
            
            # Add standard headers
            await self._add_standard_headers(formatted_response, route_info)
            
            # Handle caching headers
            await self._add_cache_headers(formatted_response, route_info)
            
            return formatted_response
            
        except Exception as e:
            logger.error(f"Error processing response for {route_info.path}: {e}")
            return response
    
    async def _format_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Format response based on route type and content"""
        try:
            # If response is already a proper HTTP response, return as-is
            if self._is_http_response(response):
                return response
            
            # Format based on route type
            if route_info.route_type == RouteType.JSON:
                return await self._format_json_response(response, route_info)
            else:
                return await self._format_http_response(response, route_info)
            
        except Exception as e:
            logger.error(f"Error formatting response: {e}")
            return response
    
    def _is_http_response(self, response: Any) -> bool:
        """Check if response is already an HTTP response object"""
        try:
            # Check for common HTTP response types
            response_types = (
                'Response', 'JSONResponse', 'HTMLResponse', 
                'PlainTextResponse', 'RedirectResponse'
            )
            return any(response_type in str(type(response)) for response_type in response_types)
        except:
            return False
    
    async def _format_json_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Format response for JSON routes"""
        try:
            from fastapi.responses import JSONResponse
            
            # Handle different response types
            if isinstance(response, dict):
                return JSONResponse(content=response)
            elif isinstance(response, (list, tuple)):
                return JSONResponse(content=list(response))
            elif response is None:
                return JSONResponse(content={"result": None})
            else:
                # Try to serialize the response
                try:
                    return JSONResponse(content={"result": response})
                except TypeError:
                    # If not serializable, convert to string
                    return JSONResponse(content={"result": str(response)})
            
        except Exception as e:
            logger.error(f"Error formatting JSON response: {e}")
            from fastapi.responses import JSONResponse
            return JSONResponse(content={"error": "Response formatting error"}, status_code=500)
    
    async def _format_http_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Format response for HTTP routes"""
        try:
            from fastapi.responses import HTMLResponse, JSONResponse, PlainTextResponse
            
            # Handle different response types
            if isinstance(response, str):
                # Check if it looks like HTML
                if response.strip().startswith('<') and response.strip().endswith('>'):
                    return HTMLResponse(content=response)
                else:
                    return PlainTextResponse(content=response)
            elif isinstance(response, dict):
                return JSONResponse(content=response)
            elif isinstance(response, (list, tuple)):
                return JSONResponse(content=list(response))
            elif response is None:
                return PlainTextResponse(content="")
            else:
                # Convert to string
                return PlainTextResponse(content=str(response))
            
        except Exception as e:
            logger.error(f"Error formatting HTTP response: {e}")
            from fastapi.responses import PlainTextResponse
            return PlainTextResponse(content="Response formatting error", status_code=500)
    
    async def _add_standard_headers(self, response: Any, route_info: RouteInfo) -> None:
        """Add standard headers to response"""
        try:
            if not hasattr(response, 'headers'):
                return
            
            # Add server header
            response.headers['Server'] = 'ERP-HTTP/1.0'
            
            # Add content type if not set
            if 'content-type' not in response.headers:
                if route_info.route_type == RouteType.JSON:
                    response.headers['Content-Type'] = 'application/json'
                else:
                    # Try to detect content type from response
                    content_type = self._detect_content_type(response)
                    if content_type:
                        response.headers['Content-Type'] = content_type
            
            # Add custom headers from route metadata
            custom_headers = route_info.metadata.get('headers', {})
            if custom_headers:
                response.headers.update(custom_headers)
            
        except Exception as e:
            logger.debug(f"Error adding standard headers: {e}")
    
    async def _add_cache_headers(self, response: Any, route_info: RouteInfo) -> None:
        """Add caching headers to response"""
        try:
            if not hasattr(response, 'headers'):
                return
            
            # Get cache configuration from route metadata
            cache_config = route_info.metadata.get('cache', {})
            
            if cache_config.get('no_cache', False):
                # Disable caching
                response.headers.update({
                    'Cache-Control': 'no-cache, no-store, must-revalidate',
                    'Pragma': 'no-cache',
                    'Expires': '0'
                })
            elif cache_config.get('max_age'):
                # Set cache max age
                max_age = cache_config['max_age']
                response.headers['Cache-Control'] = f'max-age={max_age}'
            elif route_info.route_type == RouteType.JSON:
                # Default: no cache for JSON responses
                response.headers['Cache-Control'] = 'no-cache'
            
        except Exception as e:
            logger.debug(f"Error adding cache headers: {e}")
    
    def _detect_content_type(self, response: Any) -> Optional[str]:
        """Detect content type from response"""
        try:
            response_type = type(response).__name__
            
            if 'JSON' in response_type:
                return 'application/json'
            elif 'HTML' in response_type:
                return 'text/html'
            elif 'PlainText' in response_type:
                return 'text/plain'
            
            # Try to detect from content
            if hasattr(response, 'body'):
                content = response.body
                if isinstance(content, bytes):
                    content = content.decode('utf-8', errors='ignore')
                
                if isinstance(content, str):
                    content = content.strip()
                    if content.startswith('<') and content.endswith('>'):
                        return 'text/html'
                    elif content.startswith('{') or content.startswith('['):
                        return 'application/json'
            
            return None
            
        except Exception:
            return None


class CompressionMiddleware(BaseMiddleware):
    """Middleware for response compression"""
    
    def __init__(self, priority: int = 95):
        super().__init__(priority)
        self.min_size = 1024  # Minimum size to compress (1KB)
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Check if client accepts compression"""
        try:
            if hasattr(request, 'headers'):
                accept_encoding = request.headers.get('accept-encoding', '')
                # Store compression preference in request
                if hasattr(request, 'state'):
                    request.state.accepts_gzip = 'gzip' in accept_encoding.lower()
                else:
                    setattr(request, '_accepts_gzip', 'gzip' in accept_encoding.lower())
        except Exception as e:
            logger.debug(f"Error checking compression support: {e}")
        
        return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Compress response if appropriate"""
        try:
            # Check if compression is enabled in route metadata
            compression_config = route_info.metadata.get('compression', {})
            if not compression_config.get('enabled', True):
                return response
            
            # Check if response should be compressed
            if not self._should_compress(response):
                return response
            
            # Compress the response
            return await self._compress_response(response)
            
        except Exception as e:
            logger.debug(f"Error compressing response: {e}")
            return response
    
    def _should_compress(self, response: Any) -> bool:
        """Check if response should be compressed"""
        try:
            # Check if response has content
            if not hasattr(response, 'body'):
                return False
            
            # Check content size
            content_length = len(response.body) if hasattr(response.body, '__len__') else 0
            if content_length < self.min_size:
                return False
            
            # Check content type
            if hasattr(response, 'headers'):
                content_type = response.headers.get('content-type', '')
                # Only compress text-based content
                compressible_types = ['text/', 'application/json', 'application/javascript']
                if not any(ct in content_type for ct in compressible_types):
                    return False
            
            return True
            
        except Exception:
            return False
    
    async def _compress_response(self, response: Any) -> Any:
        """Compress response content"""
        try:
            import gzip
            
            if hasattr(response, 'body') and hasattr(response, 'headers'):
                # Compress the body
                if isinstance(response.body, str):
                    body_bytes = response.body.encode('utf-8')
                else:
                    body_bytes = response.body
                
                compressed_body = gzip.compress(body_bytes)
                
                # Update response
                response.body = compressed_body
                response.headers['Content-Encoding'] = 'gzip'
                response.headers['Content-Length'] = str(len(compressed_body))
            
            return response
            
        except Exception as e:
            logger.debug(f"Error compressing response: {e}")
            return response
