"""
Odoo-style HTTP route decorator with comprehensive routing support
Refactored to use modular components for better maintainability
"""

# Import all components from the modular structure
from .metadata import RouteType, HttpMethod, RouteMetadata, create_route_metadata
from .decorators import route, systemRoute
from .registry import (
    SystemRouteRegistry,
    get_system_route_registry,
    get_database_route_registry,
    get_all_database_routes,
    register_route_for_database
)
from .fastapi_integration import setup_http_routes, RouteIntegration
from .request_processor import RequestProcessor, create_request_context
from .response_processor import ResponseProcessor, FastAPIResponseProcessor
from .auth import AuthType
from ..logging import get_logger

logger = get_logger(__name__)

# Backward compatibility - re-export the global system route registry
_system_route_registry = get_system_route_registry()


# The route decorator is now imported from decorators.py
# This is kept here for backward compatibility and documentation


# The systemRoute decorator is now imported from decorators.py
# This is kept here for backward compatibility and documentation


# Database route management functions are now in registry.py
# These are kept here for backward compatibility


# RouteIntegration class and setup_http_routes function are now in fastapi_integration.py
# These are kept here for backward compatibility
