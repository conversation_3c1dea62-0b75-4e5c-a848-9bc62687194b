"""
Response processing utilities for HTTP routes
"""

from typing import Any, Union
from fastapi import Response, HTTPException
from fastapi.responses import JSONResponse, HTMLResponse, PlainTextResponse

from .metadata import RouteType
from ..logging import get_logger

logger = get_logger(__name__)


class ResponseProcessor:
    """Handles response processing for HTTP routes"""
    
    @staticmethod
    def process_route_result(result: Any, route_type: RouteType) -> Union[Response, Any]:
        """
        Process route result based on route type
        
        Args:
            result: Result from route handler
            route_type: Type of route (HTTP or JSON)
            
        Returns:
            Processed response
        """
        if route_type == RouteType.JSON:
            # JSON RPC response handling is done in jsonrpc.py
            return result
        elif route_type == RouteType.HTTP:
            return ResponseProcessor._process_http_result(result)
        
        return result
    
    @staticmethod
    def _process_http_result(result: Any) -> Response:
        """
        Process HTTP route result into appropriate response
        
        Args:
            result: Result from HTTP route handler
            
        Returns:
            FastAPI Response object
        """
        # Handle different response types
        if isinstance(result, (Response, HTMLResponse, JSONResponse, PlainTextResponse)):
            return result
        elif isinstance(result, dict):
            return JSONResponse(content=result)
        elif isinstance(result, str):
            return HTMLResponse(content=result)
        else:
            return JSONResponse(content={"result": result})
    
    @staticmethod
    def handle_route_error(error: Exception, route_type: RouteType, path: str) -> Union[Response, HTTPException]:
        """
        Handle errors that occur during route processing
        
        Args:
            error: Exception that occurred
            route_type: Type of route (HTTP or JSON)
            path: Route path for logging
            
        Returns:
            Error response or raises HTTPException
        """
        logger.error(f"Error in route {path}: {error}")
        
        if route_type == RouteType.JSON:
            # Return JSON RPC error
            try:
                from .jsonrpc import JsonRpcError
                return JsonRpcError.internal_error(str(error)).to_response()
            except ImportError:
                logger.warning("JsonRpcError not available, falling back to HTTP error")
                raise HTTPException(status_code=500, detail=str(error))
        else:
            raise HTTPException(status_code=500, detail=str(error))


class FastAPIResponseProcessor:
    """Handles response processing for FastAPI integration"""
    
    @staticmethod
    def create_fastapi_response(result: Any) -> Response:
        """
        Create FastAPI-compatible response from result
        
        Args:
            result: Result from handler
            
        Returns:
            FastAPI Response object
        """
        if isinstance(result, (Response, HTMLResponse, JSONResponse, PlainTextResponse)):
            return result
        elif isinstance(result, dict):
            return JSONResponse(content=result)
        elif isinstance(result, str):
            return HTMLResponse(content=result)
        else:
            return JSONResponse(content={"result": result})


class ErrorResponseFactory:
    """Factory for creating error responses"""
    
    @staticmethod
    def create_json_rpc_error(error: Exception) -> Response:
        """
        Create JSON RPC error response
        
        Args:
            error: Exception to convert
            
        Returns:
            JSON RPC error response
        """
        try:
            from .jsonrpc import JsonRpcError
            return JsonRpcError.internal_error(str(error)).to_response()
        except ImportError:
            # Fallback to regular JSON response if JsonRpcError not available
            return JSONResponse(
                status_code=500,
                content={"error": {"code": -32603, "message": str(error)}}
            )
    
    @staticmethod
    def create_http_error(error: Exception, status_code: int = 500) -> HTTPException:
        """
        Create HTTP error response
        
        Args:
            error: Exception to convert
            status_code: HTTP status code
            
        Returns:
            HTTPException
        """
        return HTTPException(status_code=status_code, detail=str(error))
    
    @staticmethod
    def create_authentication_error() -> JSONResponse:
        """
        Create authentication error response
        
        Returns:
            JSON response with 401 status
        """
        return JSONResponse(
            status_code=401,
            content={"error": "Authentication required"}
        )
    
    @staticmethod
    def create_authorization_error() -> JSONResponse:
        """
        Create authorization error response
        
        Returns:
            JSON response with 403 status
        """
        return JSONResponse(
            status_code=403,
            content={"error": "Insufficient permissions"}
        )
