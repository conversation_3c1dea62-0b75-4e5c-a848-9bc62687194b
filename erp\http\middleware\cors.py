"""
CORS middleware
Handles Cross-Origin Resource Sharing for routes
"""

from typing import Any, List, Optional

from .base import BaseMiddleware
from ..interfaces import RouteInfo
from ...logging import get_logger

logger = get_logger(__name__)


class CorsMiddleware(BaseMiddleware):
    """Middleware for handling CORS"""
    
    def __init__(self, priority: int = 15):
        super().__init__(priority)
        self.default_origins = ['*']
        self.default_methods = ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS']
        self.default_headers = ['Content-Type', 'Authorization']
    
    async def process_request(self, request: Any, route_info: RouteInfo) -> Any:
        """Process CORS for request"""
        try:
            # Handle preflight OPTIONS requests
            if hasattr(request, 'method') and request.method == 'OPTIONS':
                # This is a preflight request
                return await self._handle_preflight(request, route_info)
            
            return request
            
        except Exception as e:
            logger.error(f"Error in CORS middleware for {route_info.path}: {e}")
            return request
    
    async def process_response(self, response: Any, route_info: RouteInfo) -> Any:
        """Add CORS headers to response"""
        try:
            # Get CORS configuration from route metadata
            cors_config = route_info.metadata.get('cors', {})
            
            # Add CORS headers
            self._add_cors_headers(response, cors_config, route_info)
            
            return response
            
        except Exception as e:
            logger.error(f"Error adding CORS headers for {route_info.path}: {e}")
            return response
    
    async def _handle_preflight(self, request: Any, route_info: RouteInfo) -> Any:
        """Handle CORS preflight request"""
        try:
            from fastapi.responses import Response
            
            # Get CORS configuration
            cors_config = route_info.metadata.get('cors', {})
            
            # Create preflight response
            response = Response(status_code=200)
            
            # Add CORS headers
            self._add_cors_headers(response, cors_config, route_info)
            
            # Add preflight-specific headers
            allowed_methods = cors_config.get('methods', route_info.methods)
            response.headers['Access-Control-Allow-Methods'] = ', '.join(allowed_methods)
            
            return response
            
        except Exception as e:
            logger.error(f"Error handling CORS preflight for {route_info.path}: {e}")
            # Return a basic response
            from fastapi.responses import Response
            return Response(status_code=200)
    
    def _add_cors_headers(self, response: Any, cors_config: dict, route_info: RouteInfo) -> None:
        """Add CORS headers to response"""
        try:
            if not hasattr(response, 'headers'):
                return
            
            # Get allowed origins
            allowed_origins = cors_config.get('origins', self.default_origins)
            if isinstance(allowed_origins, str):
                allowed_origins = [allowed_origins]
            
            # Set Access-Control-Allow-Origin
            if '*' in allowed_origins:
                response.headers['Access-Control-Allow-Origin'] = '*'
            else:
                # For specific origins, we'd need to check the request origin
                # For simplicity, using the first allowed origin
                response.headers['Access-Control-Allow-Origin'] = allowed_origins[0]
            
            # Set other CORS headers
            allowed_headers = cors_config.get('headers', self.default_headers)
            if allowed_headers:
                response.headers['Access-Control-Allow-Headers'] = ', '.join(allowed_headers)
            
            # Allow credentials if specified
            if cors_config.get('credentials', False):
                response.headers['Access-Control-Allow-Credentials'] = 'true'
            
            # Set max age for preflight cache
            max_age = cors_config.get('max_age', 86400)  # 24 hours default
            response.headers['Access-Control-Max-Age'] = str(max_age)
            
            # Expose headers if specified
            expose_headers = cors_config.get('expose_headers', [])
            if expose_headers:
                response.headers['Access-Control-Expose-Headers'] = ', '.join(expose_headers)
            
        except Exception as e:
            logger.debug(f"Error setting CORS headers: {e}")
    
    def _get_request_origin(self, request: Any) -> Optional[str]:
        """Get origin from request"""
        try:
            if hasattr(request, 'headers'):
                return request.headers.get('Origin')
            return None
        except Exception:
            return None
    
    def _is_origin_allowed(self, origin: str, allowed_origins: List[str]) -> bool:
        """Check if origin is allowed"""
        if '*' in allowed_origins:
            return True
        
        return origin in allowed_origins


class CorsConfigBuilder:
    """Builder for CORS configuration"""
    
    def __init__(self):
        self.config = {}
    
    def allow_origins(self, origins: List[str]) -> 'CorsConfigBuilder':
        """Set allowed origins"""
        self.config['origins'] = origins
        return self
    
    def allow_methods(self, methods: List[str]) -> 'CorsConfigBuilder':
        """Set allowed methods"""
        self.config['methods'] = methods
        return self
    
    def allow_headers(self, headers: List[str]) -> 'CorsConfigBuilder':
        """Set allowed headers"""
        self.config['headers'] = headers
        return self
    
    def allow_credentials(self, allow: bool = True) -> 'CorsConfigBuilder':
        """Set whether to allow credentials"""
        self.config['credentials'] = allow
        return self
    
    def max_age(self, seconds: int) -> 'CorsConfigBuilder':
        """Set max age for preflight cache"""
        self.config['max_age'] = seconds
        return self
    
    def expose_headers(self, headers: List[str]) -> 'CorsConfigBuilder':
        """Set headers to expose"""
        self.config['expose_headers'] = headers
        return self
    
    def build(self) -> dict:
        """Build the CORS configuration"""
        return self.config.copy()


# Predefined CORS configurations
CORS_ALLOW_ALL = CorsConfigBuilder().allow_origins(['*']).allow_methods(['*']).allow_headers(['*']).build()

CORS_STRICT = CorsConfigBuilder().allow_origins([]).allow_methods(['GET']).allow_headers(['Content-Type']).build()

CORS_API = CorsConfigBuilder().allow_origins(['*']).allow_methods(['GET', 'POST', 'PUT', 'DELETE']).allow_headers(['Content-Type', 'Authorization']).build()
