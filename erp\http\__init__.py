"""
HTTP routing system for ERP
Provides modular, extensible HTTP routing with Odoo-style decorators and JSON RPC 2.0 support
"""

# Core interfaces and types
from .interfaces import (
    IRouteRegistry, IRouteHandler, IRouteDiscovery, IRouteValidator,
    IMiddleware, IRouteAdapter, IRouteLifecycle, IHealthCheck,
    RouteInfo, RouteScope, RouteRegistrationError, RouteValidationError,
    RouteNotFoundError, MiddlewareError
)

# Metadata and enums
from .metadata import RouteType, HttpMethod, AuthType, RouteMetadata, create_route_metadata

# Decorators
from .decorators import route, systemRoute

# Registries
from .registries import (
    SystemRouteRegistry, get_system_route_registry, reset_system_route_registry,
    DatabaseRouteRegistry, DatabaseRouteManager, get_database_route_manager
)

# Controllers
from .controllers import (
    BaseController, Controller, MinimalController, APIController,
    TemplateController, DatabaseController, ControllerRegistry, get_controller_registry
)

# Factories
from .factories import (
    RouteHandlerFactory, RouteInfoFactory, get_route_handler_factory, get_route_info_factory
)

# Middleware
from .middleware import (
    MiddlewarePipeline, get_middleware_pipeline, BaseMiddleware,
    AuthMiddleware, CorsMiddleware, RequestProcessingMiddleware, ResponseProcessingMiddleware
)

# Adapters
from .adapters import FastAPIRouteAdapter, FastAPIResponseTransformer, BaseRouteAdapter

# Services
from .discovery import RouteDiscoveryService, RouteOrganizer, get_route_discovery_service, get_route_organizer
from .validation import RouteValidator, get_route_validator
from .health import RouteHealthChecker, HealthStatus, get_health_checker

# JSON RPC
from .jsonrpc import JsonRpcHandler, JsonRpcError, JsonRpcRequest, JsonRpcResponse

# Auth and CORS (legacy)
from .auth import AuthType as LegacyAuthType, require_auth
from .cors import cors_handler

__all__ = [
    # Core interfaces
    'IRouteRegistry', 'IRouteHandler', 'IRouteDiscovery', 'IRouteValidator',
    'IMiddleware', 'IRouteAdapter', 'IRouteLifecycle', 'IHealthCheck',
    'RouteInfo', 'RouteScope',

    # Exceptions
    'RouteRegistrationError', 'RouteValidationError', 'RouteNotFoundError', 'MiddlewareError',

    # Metadata and enums
    'RouteType', 'HttpMethod', 'AuthType', 'RouteMetadata', 'create_route_metadata',

    # Decorators
    'route', 'systemRoute',

    # Registries
    'SystemRouteRegistry', 'get_system_route_registry', 'reset_system_route_registry',
    'DatabaseRouteRegistry', 'DatabaseRouteManager', 'get_database_route_manager',

    # Controllers
    'BaseController', 'Controller', 'MinimalController', 'APIController',
    'TemplateController', 'DatabaseController', 'ControllerRegistry', 'get_controller_registry',

    # Factories
    'RouteHandlerFactory', 'RouteInfoFactory', 'get_route_handler_factory', 'get_route_info_factory',

    # Middleware
    'MiddlewarePipeline', 'get_middleware_pipeline', 'BaseMiddleware',
    'AuthMiddleware', 'CorsMiddleware', 'RequestProcessingMiddleware', 'ResponseProcessingMiddleware',

    # Adapters
    'FastAPIRouteAdapter', 'FastAPIResponseTransformer', 'BaseRouteAdapter',

    # Services
    'RouteDiscoveryService', 'RouteOrganizer', 'get_route_discovery_service', 'get_route_organizer',
    'RouteValidator', 'get_route_validator',
    'RouteHealthChecker', 'HealthStatus', 'get_health_checker',

    # JSON RPC
    'JsonRpcHandler', 'JsonRpcError', 'JsonRpcRequest', 'JsonRpcResponse',

    # Legacy auth and CORS
    'LegacyAuthType', 'require_auth', 'cors_handler',
]
