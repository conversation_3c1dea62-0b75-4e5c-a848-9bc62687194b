# HTTP Module Refactoring Migration Guide

This document provides guidance for migrating from the old monolithic HTTP module to the new modular architecture.

## Overview of Changes

The HTTP module has been refactored to eliminate monolithic behavior and improve maintainability:

### Key Improvements

1. **Separation of Concerns**: Large classes split into focused components
2. **Interface-Based Design**: Clear contracts between components
3. **Middleware Pipeline**: Composable request/response processing
4. **Factory Patterns**: Decoupled object creation
5. **Service-Oriented Architecture**: Dedicated services for validation, health checks, etc.

## Migration Steps

### 1. Update Imports

**Old:**
```python
from erp.http import route, Controller, SystemRouteRegistry
from erp.http.registry import get_system_route_registry
from erp.http.controller import ControllerRegistry
```

**New:**
```python
from erp.http import route, Controller  # Still works
from erp.http.registries import get_system_route_registry  # New location
from erp.http.controllers import ControllerRegistry  # New location
```

### 2. Controller Updates

**Old Monolithic Controller:**
```python
class MyController(Controller):
    def my_route(self, request):
        # All functionality mixed together
        params = self.params
        user = self.get_current_user()
        html = self.render_template('template.html', {})
        return html
```

**New Modular Controller:**
```python
# Option 1: Use full Controller (same as before)
class MyController(Controller):
    def my_route(self, request):
        # Same code works
        params = self.params
        user = self.get_current_user()
        html = self.render_template('template.html', {})
        return html

# Option 2: Use specialized controllers
class MyAPIController(APIController):  # Only API functionality
    def my_api_route(self, request):
        self.validate_required_params(['param1'])
        return self.json_response({'status': 'ok'})

class MyTemplateController(TemplateController):  # Only template functionality
    def my_template_route(self, request):
        return self.render_template('template.html', {})
```

### 3. Route Registration

**Old:**
```python
registry = get_system_route_registry()
registry.register_route('/path', handler, methods=['GET'])
```

**New:**
```python
from erp.http.registries import get_system_route_registry
from erp.http.factories import get_route_info_factory
from erp.http.interfaces import RouteScope

registry = get_system_route_registry()
factory = get_route_info_factory()

route_info = factory.create_route_info(
    path='/path',
    handler=handler,
    methods=['GET'],
    scope=RouteScope.SYSTEM
)

await registry.register_route(route_info)
```

### 4. Middleware Usage

**Old (monolithic processors):**
```python
from erp.http.request_processor import RequestProcessor
from erp.http.response_processor import ResponseProcessor

# Manual processing
processed_request = RequestProcessor.process(request)
response = handler(processed_request)
processed_response = ResponseProcessor.process(response)
```

**New (middleware pipeline):**
```python
from erp.http.middleware import get_middleware_pipeline

pipeline = get_middleware_pipeline()
response = await pipeline.execute_pipeline(request, route_info, handler)
```

### 5. FastAPI Integration

**Old:**
```python
from erp.http.fastapi_integration import RouteIntegration

RouteIntegration.register_routes_with_fastapi(app, registry)
```

**New:**
```python
from erp.http.integration import setup_http_routes

await setup_http_routes(app)
```

## Backward Compatibility

The refactoring maintains backward compatibility:

### Existing Code Continues to Work

- All existing decorators (`@route`, `@systemRoute`) work unchanged
- Controller classes work with the same API
- Import paths are maintained with deprecation warnings

### Deprecation Warnings

Some modules now issue deprecation warnings:

```python
# This works but shows a warning
from erp.http.registry import SystemRouteRegistry

# Use this instead
from erp.http.registries import SystemRouteRegistry
```

## New Features

### 1. Validation Service

```python
from erp.http.validation import get_route_validator

validator = get_route_validator()
errors = await validator.validate_route(route_info)
```

### 2. Health Checks

```python
from erp.http.health import get_health_checker

health_checker = get_health_checker()
health = await health_checker.check_registry_health(registry)
```

### 3. Route Discovery

```python
from erp.http.discovery import get_route_discovery_service

discovery = get_route_discovery_service()
routes = await discovery.discover_routes('my.module', RouteScope.SYSTEM)
```

### 4. Custom Middleware

```python
from erp.http.middleware import BaseMiddleware, get_middleware_pipeline

class CustomMiddleware(BaseMiddleware):
    def __init__(self):
        super().__init__(priority=50)
    
    async def process_request(self, request, route_info):
        # Custom request processing
        return request
    
    async def process_response(self, response, route_info):
        # Custom response processing
        return response

pipeline = get_middleware_pipeline()
await pipeline.add_middleware(CustomMiddleware())
```

## Testing Updates

### Old Tests

```python
def test_route():
    registry = get_system_route_registry()
    registry.register_route('/test', handler)
    routes = registry.get_routes()
    assert '/test' in routes
```

### New Tests

```python
@pytest.mark.asyncio
async def test_route():
    from erp.http.registries import get_system_route_registry, reset_system_route_registry
    from erp.http.factories import get_route_info_factory
    
    await reset_system_route_registry()  # Clean state
    
    registry = get_system_route_registry()
    factory = get_route_info_factory()
    
    route_info = factory.create_route_info(
        path='/test',
        handler=handler,
        scope=RouteScope.SYSTEM
    )
    
    await registry.register_route(route_info)
    routes = await registry.get_routes()
    assert '/test' in routes
```

## Performance Improvements

The new architecture provides several performance benefits:

1. **Lazy Loading**: Components are initialized only when needed
2. **Async-First**: All operations are async by default
3. **Caching**: Health checks and validation results are cached
4. **Pipeline Optimization**: Middleware can be conditionally applied

## Troubleshooting

### Common Issues

1. **Import Errors**: Update import paths as shown above
2. **Async Errors**: Ensure you're using `await` with new async methods
3. **Registry Errors**: Use the new RouteInfo objects instead of raw dictionaries

### Getting Help

- Check the deprecation warnings for guidance
- Review the test files for usage examples
- Use the new validation service to check your routes

## Future Deprecations

The following will be deprecated in future versions:

- `erp.http.registry` module (use `erp.http.registries`)
- `erp.http.controller` module (use `erp.http.controllers`)
- Synchronous route registration methods

Plan to migrate to the new APIs to avoid future breaking changes.
